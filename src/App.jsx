import React, { useState } from 'react';
import { AppProvider } from './context/AppContext';
import Header from './components/Header';
import Navigation from './components/Navigation';
import HomePage from './pages/HomePage';
import ComediansPage from './pages/ComediansPage';
import ComedianDetailPage from './pages/ComedianDetailPage';
import EventsPage from './pages/EventsPage';
import ArticlesPage from './pages/ArticlesPage';
import AdminPage from './pages/AdminPage';

// メインアプリコンポーネント
function App() {
  const [currentPage, setCurrentPage] = useState('home');
  const [selectedComedianId, setSelectedComedianId] = useState(null);

  const navigateTo = (page, comedianId = null) => {
    setCurrentPage(page);
    setSelectedComedianId(comedianId);
  };

  const renderPage = () => {
    switch (currentPage) {
      case 'home':
        return <HomePage navigateTo={navigateTo} />;
      case 'comedians':
        return <ComediansPage navigateTo={navigateTo} />;
      case 'comedian-detail':
        return <ComedianDetailPage comedianId={selectedComedianId} />;
      case 'events':
        return <EventsPage />;
      case 'articles':
        return <ArticlesPage />;
      case 'admin':
        return <AdminPage />;
      default:
        return <HomePage navigateTo={navigateTo} />;
    }
  };

  return (
    <AppProvider>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <Header />
        <Navigation currentPage={currentPage} navigateTo={navigateTo} />

        {/* メインコンテンツ */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {renderPage()}
        </main>
      </div>
    </AppProvider>
  );
}

export default App;