import React, { useState, createContext, useContext } from 'react';

// アプリの状態管理用Context
const AppContext = createContext();

// ダミーデータ
const initialData = {
  comedians: [
    {
      id: 1,
      name: "明石家さんま",
      image: "https://via.placeholder.com/200x200?text=さんま",
      bio: "関西お笑い界の重鎮。トーク番組の司会者としても活躍。",
      category: "関西芸人",
      socialLinks: [
        { platform: "Twitter", url: "https://twitter.com/sanma" },
        { platform: "Instagram", url: "https://instagram.com/sanma" }
      ]
    },
    {
      id: 2,
      name: "ダウンタウン",
      image: "https://via.placeholder.com/200x200?text=DT",
      bio: "松本人志と浜田雅功のコンビ。バラエティ番組で絶大な人気。",
      category: "漫才コンビ",
      socialLinks: [
        { platform: "Twitter", url: "https://twitter.com/downtown" }
      ]
    },
    {
      id: 3,
      name: "有吉弘行",
      image: "https://via.placeholder.com/200x200?text=有吉",
      bio: "毒舌キャラで人気のお笑い芸人。ラジオパーソナリティとしても活動。",
      category: "ピン芸人",
      socialLinks: [
        { platform: "Twitter", url: "https://twitter.com/ariyoshi" },
        { platform: "YouTube", url: "https://youtube.com/ariyoshi" }
      ]
    }
  ],
  events: [
    {
      id: 1,
      comedianId: 1,
      eventName: "さんまのお笑い向上委員会",
      date: "2024-12-15",
      venue: "フジテレビ",
      detailsURL: "https://example.com/event1",
      isApproved: true,
      submittedBy: "ファンA"
    },
    {
      id: 2,
      comedianId: 2,
      eventName: "ガキの使いやあらへんで",
      date: "2024-12-20",
      venue: "日本テレビ",
      detailsURL: "https://example.com/event2",
      isApproved: false,
      submittedBy: "ファンB"
    }
  ],
  articles: [
    {
      id: 1,
      comedianId: 1,
      title: "さんまさんの意外な一面",
      content: "先日のラジオで語られたエピソードが印象的でした。普段は明るいキャラクターで知られるさんまさんですが、実は読書家でもあり、哲学書なども愛読されているそうです。",
      imageURL: "https://via.placeholder.com/300x200?text=記事画像",
      isApproved: true,
      submittedBy: "ファンC"
    },
    {
      id: 2,
      comedianId: 3,
      title: "有吉さんの新番組について",
      content: "有吉さんの新しい深夜番組がスタートします。これまでとは違った一面を見せてくれる企画が盛りだくさんとのことで、ファンの間では大きな話題となっています。",
      imageURL: "https://via.placeholder.com/300x200?text=記事画像2",
      isApproved: false,
      submittedBy: "ファンD"
    }
  ]
};

// メインアプリコンポーネント
function App() {
  const [data, setData] = useState(initialData);
  const [currentPage, setCurrentPage] = useState('home');
  const [selectedComedianId, setSelectedComedianId] = useState(null);

  const updateData = (newData) => {
    setData(newData);
  };

  const navigateTo = (page, comedianId = null) => {
    setCurrentPage(page);
    setSelectedComedianId(comedianId);
  };

  const renderPage = () => {
    switch (currentPage) {
      case 'home':
        return <HomePage navigateTo={navigateTo} />;
      case 'comedians':
        return <ComediansPage navigateTo={navigateTo} />;
      case 'comedian-detail':
        return <ComedianDetailPage comedianId={selectedComedianId} />;
      case 'events':
        return <EventsPage />;
      case 'articles':
        return <ArticlesPage />;
      case 'admin':
        return <AdminPage />;
      default:
        return <HomePage navigateTo={navigateTo} />;
    }
  };

  return (
    <AppContext.Provider value={{ data, updateData }}>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
        {/* ヘッダー */}
        <header className="bg-white shadow-lg border-b border-gray-100">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center space-x-4">
                <div className="bg-gradient-to-r from-purple-600 to-blue-600 p-2 rounded-lg">
                  <span className="text-white text-xl font-bold">🎭</span>
                </div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                  お笑い百科事典
                </h1>
              </div>
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="芸人名を検索..."
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-full focus:ring-2 focus:ring-purple-500 focus:border-transparent w-64"
                  />
                  <div className="absolute left-3 top-2.5 text-gray-400">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                </div>
                <button className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-2 rounded-full hover:from-purple-700 hover:to-blue-700 transition-all">
                  ログイン
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* ナビゲーション */}
        <nav className="bg-white border-b border-gray-200 sticky top-0 z-40">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex space-x-8 overflow-x-auto">
              {[
                { key: 'home', label: 'トップ', icon: '🏠' },
                { key: 'comedians', label: '芸人一覧', icon: '👥' },
                { key: 'events', label: '出演情報', icon: '📅' },
                { key: 'articles', label: '記事一覧', icon: '📰' },
                { key: 'admin', label: '管理画面', icon: '⚙️' }
              ].map(item => (
                <button
                  key={item.key}
                  onClick={() => navigateTo(item.key)}
                  className={`flex items-center space-x-2 py-4 px-2 text-sm font-medium border-b-2 transition-all ${currentPage === item.key
                      ? 'border-purple-500 text-purple-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                >
                  <span>{item.icon}</span>
                  <span>{item.label}</span>
                </button>
              ))}
            </div>
          </div>
        </nav>

        {/* メインコンテンツ */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {renderPage()}
        </main>
      </div>
    </AppContext.Provider>
  );
}

// トップページ
function HomePage({ navigateTo }) {
  const { data } = useContext(AppContext);
  const featuredComedian = data.comedians[0];

  return (
    <div className="space-y-8">
      {/* ヒーローセクション */}
      <div className="bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 rounded-3xl p-8 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="relative z-10">
          <h2 className="text-4xl font-bold mb-4">お笑い芸人の情報が満載！</h2>
          <p className="text-xl opacity-90 mb-6">ファンによる、ファンのための情報共有サイト</p>
          <button
            onClick={() => navigateTo('comedians')}
            className="bg-white text-purple-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-all transform hover:scale-105"
          >
            芸人一覧を見る
          </button>
        </div>
        <div className="absolute right-0 top-0 w-64 h-64 bg-white opacity-10 rounded-full -mr-32 -mt-32"></div>
        <div className="absolute right-20 bottom-0 w-32 h-32 bg-white opacity-10 rounded-full -mb-16"></div>
      </div>

      {/* 注目の芸人 */}
      <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
            <span className="text-white text-sm">⭐</span>
          </div>
          <h3 className="text-2xl font-bold text-gray-800">本日の注目芸人</h3>
        </div>
        <div className="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-8">
          <div className="relative">
            <img
              src={featuredComedian.image}
              alt={featuredComedian.name}
              className="w-32 h-32 object-cover rounded-2xl shadow-lg ring-4 ring-purple-100"
            />
            <div className="absolute -top-2 -right-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">
              1
            </div>
          </div>
          <div className="flex-1 text-center md:text-left">
            <h4 className="text-2xl font-bold text-gray-800 mb-2">{featuredComedian.name}</h4>
            <p className="text-gray-600 mb-4">{featuredComedian.bio}</p>
            <button
              onClick={() => navigateTo('comedian-detail', featuredComedian.id)}
              className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-3 rounded-full hover:from-purple-700 hover:to-blue-700 transition-all transform hover:scale-105 shadow-lg"
            >
              詳細を見る
            </button>
          </div>
        </div>
      </div>

      {/* 統計情報 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 text-center">
          <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-white text-xl">👥</span>
          </div>
          <h4 className="text-2xl font-bold text-gray-800 mb-2">{data.comedians.length}</h4>
          <p className="text-gray-600">登録芸人数</p>
        </div>
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 text-center">
          <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-white text-xl">📅</span>
          </div>
          <h4 className="text-2xl font-bold text-gray-800 mb-2">{data.events.filter(e => e.isApproved).length}</h4>
          <p className="text-gray-600">出演情報</p>
        </div>
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 text-center">
          <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-white text-xl">📰</span>
          </div>
          <h4 className="text-2xl font-bold text-gray-800 mb-2">{data.articles.filter(a => a.isApproved).length}</h4>
          <p className="text-gray-600">記事数</p>
        </div>
      </div>

      {/* お知らせ */}
      <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-2xl p-8 border border-indigo-100">
        <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
          <span className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mr-3">
            <span className="text-white text-sm">📢</span>
          </span>
          最新情報
        </h3>
        <div className="space-y-4">
          <div className="flex items-start space-x-4">
            <div className="w-2 h-2 bg-purple-500 rounded-full mt-3"></div>
            <div>
              <p className="text-gray-800 font-medium">✨ 新機能：芸人詳細ページでSNSリンクを確認できるようになりました</p>
              <p className="text-gray-600 text-sm">2024年12月1日</p>
            </div>
          </div>
          <div className="flex items-start space-x-4">
            <div className="w-2 h-2 bg-blue-500 rounded-full mt-3"></div>
            <div>
              <p className="text-gray-800 font-medium">📢 投稿された情報は管理者の承認後に公開されます</p>
              <p className="text-gray-600 text-sm">システム情報</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// 芸人一覧ページ
function ComediansPage({ navigateTo }) {
  const { data } = useContext(AppContext);
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = ['all', ...new Set(data.comedians.map(c => c.category))];
  const filteredComedians = selectedCategory === 'all'
    ? data.comedians
    : data.comedians.filter(c => c.category === selectedCategory);

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-800 mb-4">芸人一覧</h2>
        <p className="text-gray-600">お気に入りの芸人を見つけよう</p>
      </div>

      {/* カテゴリフィルター */}
      <div className="flex flex-wrap justify-center gap-3">
        {categories.map(category => (
          <button
            key={category}
            onClick={() => setSelectedCategory(category)}
            className={`px-6 py-2 rounded-full text-sm font-medium transition-all ${selectedCategory === category
                ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg'
                : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-200'
              }`}
          >
            {category === 'all' ? 'すべて' : category}
          </button>
        ))}
      </div>

      {/* 芸人カード */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {filteredComedians.map(comedian => (
          <div key={comedian.id} className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all transform hover:-translate-y-2">
            <div className="relative">
              <img
                src={comedian.image}
                alt={comedian.name}
                className="w-full h-48 object-cover"
              />
              <div className="absolute top-4 right-4 bg-white bg-opacity-90 backdrop-blur-sm px-3 py-1 rounded-full">
                <span className="text-sm font-medium text-gray-700">{comedian.category}</span>
              </div>
            </div>
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-2">{comedian.name}</h3>
              <p className="text-gray-600 mb-4 line-clamp-2">{comedian.bio}</p>
              <div className="flex items-center justify-between">
                <div className="flex space-x-2">
                  {comedian.socialLinks.slice(0, 2).map((link, index) => (
                    <div key={index} className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                      <span className="text-xs text-gray-600">
                        {link.platform === 'Twitter' ? '🐦' :
                          link.platform === 'Instagram' ? '📷' :
                            link.platform === 'YouTube' ? '🎥' : '🔗'}
                      </span>
                    </div>
                  ))}
                </div>
                <button
                  onClick={() => navigateTo('comedian-detail', comedian.id)}
                  className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-2 rounded-full text-sm hover:from-purple-700 hover:to-blue-700 transition-all"
                >
                  詳細を見る
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// 芸人詳細ページ
function ComedianDetailPage({ comedianId }) {
  const { data } = useContext(AppContext);
  const comedian = data.comedians.find(c => c.id === comedianId);
  const comedianEvents = data.events.filter(e => e.comedianId === comedian?.id && e.isApproved);
  const comedianArticles = data.articles.filter(a => a.comedianId === comedian?.id && a.isApproved);

  if (!comedian) return (
    <div className="text-center py-12">
      <div className="text-6xl mb-4">😅</div>
      <h2 className="text-2xl font-bold text-gray-800 mb-2">芸人が見つかりません</h2>
      <p className="text-gray-600">お探しの芸人は存在しないか、削除された可能性があります。</p>
    </div>
  );

  return (
    <div className="space-y-8">
      {/* プロフィールセクション */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 p-8 text-white">
          <div className="flex flex-col md:flex-row items-center space-y-6 md:space-y-0 md:space-x-8">
            <div className="relative">
              <img
                src={comedian.image}
                alt={comedian.name}
                className="w-32 h-32 object-cover rounded-2xl shadow-2xl ring-4 ring-white ring-opacity-50"
              />
            </div>
            <div className="text-center md:text-left">
              <h1 className="text-4xl font-bold mb-2">{comedian.name}</h1>
              <div className="bg-white bg-opacity-20 backdrop-blur-sm px-4 py-2 rounded-full inline-block mb-4">
                <span className="text-sm font-medium">{comedian.category}</span>
              </div>
              <p className="text-lg opacity-90">{comedian.bio}</p>
            </div>
          </div>
        </div>
        <div className="p-8">
          <h4 className="text-lg font-semibold text-gray-800 mb-4">SNSリンク</h4>
          <div className="flex flex-wrap gap-3">
            {comedian.socialLinks.map((link, index) => (
              <a
                key={index}
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 bg-gradient-to-r from-gray-100 to-gray-200 hover:from-purple-100 hover:to-blue-100 px-4 py-2 rounded-full transition-all"
              >
                <span>
                  {link.platform === 'Twitter' ? '🐦' :
                    link.platform === 'Instagram' ? '📷' :
                      link.platform === 'YouTube' ? '🎥' : '🔗'}
                </span>
                <span className="text-sm font-medium text-gray-700">{link.platform}</span>
              </a>
            ))}
          </div>
        </div>
      </div>

      {/* 出演情報セクション */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
        <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
          <span className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mr-3">
            <span className="text-white text-sm">📅</span>
          </span>
          出演情報
        </h3>
        {comedianEvents.length > 0 ? (
          <div className="space-y-4">
            {comedianEvents.map(event => (
              <div key={event.id} className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-100 rounded-xl p-6">
                <h4 className="text-xl font-semibold text-gray-800 mb-3">{event.eventName}</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2 text-gray-600">
                    <span>📅</span>
                    <span>{event.date}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-600">
                    <span>📍</span>
                    <span>{event.venue}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-600">
                    <span>👤</span>
                    <span>{event.submittedBy}</span>
                  </div>
                  {event.detailsURL && (
                    <div className="flex items-center space-x-2">
                      <a
                        href={event.detailsURL}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2 rounded-full text-sm hover:from-green-600 hover:to-emerald-600 transition-all"
                      >
                        詳細を見る
                      </a>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">📅</div>
            <p className="text-gray-600">承認済みの出演情報はありません</p>
          </div>
        )}
      </div>

      {/* 関連記事セクション */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
        <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
          <span className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mr-3">
            <span className="text-white text-sm">📰</span>
          </span>
          関連記事
        </h3>
        {comedianArticles.length > 0 ? (
          <div className="space-y-6">
            {comedianArticles.map(article => (
              <div key={article.id} className="bg-gradient-to-r from-orange-50 to-red-50 border border-orange-100 rounded-xl p-6">
                {article.imageURL && (
                  <img
                    src={article.imageURL}
                    alt={article.title}
                    className="w-full h-48 object-cover rounded-lg mb-4"
                  />
                )}
                <h4 className="text-xl font-semibold text-gray-800 mb-3">{article.title}</h4>
                <p className="text-gray-600 leading-relaxed mb-4">{article.content}</p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 text-gray-500 text-sm">
                    <span>👤</span>
                    <span>{article.submittedBy}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">📰</div>
            <p className="text-gray-600">承認済みの記事はありません</p>
          </div>
        )}
      </div>
    </div>
  );
}

// 出演情報ページ
function EventsPage() {
  const { data, updateData } = useContext(AppContext);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({
    comedianId: '',
    eventName: '',
    date: '',
    venue: '',
    detailsURL: '',
    submittedBy: ''
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    const newEvent = {
      id: Date.now(),
      ...formData,
      comedianId: parseInt(formData.comedianId),
      isApproved: false
    };

    const newData = {
      ...data,
      events: [...data.events, newEvent]
    };
    updateData(newData);
    setFormData({ comedianId: '', eventName: '', date: '', venue: '', detailsURL: '', submittedBy: '' });
    setShowForm(false);
    alert('出演情報を投稿しました！管理者の承認をお待ちください。');
  };

  const approvedEvents = data.events.filter(e => e.isApproved);

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-800 mb-4">出演情報一覧</h2>
        <p className="text-gray-600">最新の出演情報をチェックしよう</p>
      </div>

      {/* 投稿フォームトグルボタン */}
      <div className="flex justify-center">
        <button
          onClick={() => setShowForm(!showForm)}
          className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-2 rounded-full font-semibold shadow-lg hover:from-purple-700 hover:to-blue-700 transition-all"
        >
          {showForm ? '投稿フォームを閉じる' : '新規出演情報を投稿'}
        </button>
      </div>

      {/* 投稿フォーム */}
      {showForm && (
        <form
          onSubmit={handleSubmit}
          className="bg-white rounded-2xl shadow-lg border border-gray-100 max-w-xl mx-auto p-8 space-y-6"
        >
          <div>
            <label className="block mb-1 font-semibold text-gray-700">芸人</label>
            <select
              value={formData.comedianId}
              onChange={e => setFormData({ ...formData, comedianId: e.target.value })}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-purple-400"
              required
            >
              <option value="">芸人を選択</option>
              {data.comedians.map(comedian => (
                <option key={comedian.id} value={comedian.id}>
                  {comedian.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block mb-1 font-semibold text-gray-700">イベント名</label>
            <input
              type="text"
              value={formData.eventName}
              onChange={e => setFormData({ ...formData, eventName: e.target.value })}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-purple-400"
              placeholder="イベント名を入力"
              required
            />
          </div>
          <div>
            <label className="block mb-1 font-semibold text-gray-700">日時</label>
            <input
              type="date"
              value={formData.date}
              onChange={e => setFormData({ ...formData, date: e.target.value })}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-purple-400"
              required
            />
          </div>
          <div>
            <label className="block mb-1 font-semibold text-gray-700">会場</label>
            <input
              type="text"
              value={formData.venue}
              onChange={e => setFormData({ ...formData, venue: e.target.value })}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-purple-400"
              placeholder="会場名を入力"
              required
            />
          </div>
          <div>
            <label className="block mb-1 font-semibold text-gray-700">詳細URL（任意）</label>
            <input
              type="url"
              value={formData.detailsURL}
              onChange={e => setFormData({ ...formData, detailsURL: e.target.value })}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-purple-400"
              placeholder="https://example.com"
            />
          </div>
          <div>
            <label className="block mb-1 font-semibold text-gray-700">投稿者名</label>
            <input
              type="text"
              value={formData.submittedBy}
              onChange={e => setFormData({ ...formData, submittedBy: e.target.value })}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-purple-400"
              placeholder="あなたの名前"
              required
            />
          </div>
          <button
            type="submit"
            className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-2 rounded-full font-semibold shadow-lg hover:from-purple-700 hover:to-blue-700 transition-all"
          >
            投稿
          </button>
        </form>
      )}

      {/* 承認済みイベント一覧 */}
      <div>
        <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
          <span className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mr-3">
            <span className="text-white text-sm">📅</span>
          </span>
          承認済み出演情報
        </h3>
        {approvedEvents.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">📝</div>
            <p className="text-gray-600">まだ承認済みの出演情報がありません。</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {approvedEvents.map(event => {
              const comedian = data.comedians.find(c => c.id === event.comedianId);
              return (
                <div
                  key={event.id}
                  className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-100 rounded-xl p-6 shadow-md"
                >
                  <h4 className="text-xl font-semibold text-gray-800 mb-2">{event.eventName}</h4>
                  <div className="flex flex-col space-y-2 text-gray-600">
                    <span>🎭 芸人: {comedian?.name}</span>
                    <span>📅 日時: {event.date}</span>
                    <span>📍 会場: {event.venue}</span>
                    <span>👤 投稿者: {event.submittedBy}</span>
                    {event.detailsURL && (
                      <a
                        href={event.detailsURL}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-block mt-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2 rounded-full text-sm hover:from-green-600 hover:to-emerald-600 transition-all"
                      >
                        詳細を見る
                      </a>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}
export default App;