import React from 'react';
import { useAppContext } from '../context/AppContext';

function AdminPage() {
  const { data, updateData } = useAppContext();
  
  const pendingEvents = data.events.filter(e => !e.isApproved);
  const pendingArticles = data.articles.filter(a => !a.isApproved);

  const approveEvent = (eventId) => {
    const newData = {
      ...data,
      events: data.events.map(event =>
        event.id === eventId ? { ...event, isApproved: true } : event
      )
    };
    updateData(newData);
  };

  const rejectEvent = (eventId) => {
    const newData = {
      ...data,
      events: data.events.filter(event => event.id !== eventId)
    };
    updateData(newData);
  };

  const approveArticle = (articleId) => {
    const newData = {
      ...data,
      articles: data.articles.map(article =>
        article.id === articleId ? { ...article, isApproved: true } : article
      )
    };
    updateData(newData);
  };

  const rejectArticle = (articleId) => {
    const newData = {
      ...data,
      articles: data.articles.filter(article => article.id !== articleId)
    };
    updateData(newData);
  };

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-800 mb-4">管理画面</h2>
        <p className="text-gray-600">投稿された情報の承認・却下を行います</p>
      </div>

      {/* 承認待ち出演情報 */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
        <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
          <span className="w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mr-3">
            <span className="text-white text-sm">⏳</span>
          </span>
          承認待ち出演情報 ({pendingEvents.length})
        </h3>
        {pendingEvents.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">✅</div>
            <p className="text-gray-600">承認待ちの出演情報はありません。</p>
          </div>
        ) : (
          <div className="space-y-4">
            {pendingEvents.map(event => {
              const comedian = data.comedians.find(c => c.id === event.comedianId);
              return (
                <div key={event.id} className="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
                  <h4 className="text-xl font-semibold text-gray-800 mb-3">{event.eventName}</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div className="flex items-center space-x-2 text-gray-600">
                      <span>🎭</span>
                      <span>芸人: {comedian?.name}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-gray-600">
                      <span>📅</span>
                      <span>日時: {event.date}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-gray-600">
                      <span>📍</span>
                      <span>会場: {event.venue}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-gray-600">
                      <span>👤</span>
                      <span>投稿者: {event.submittedBy}</span>
                    </div>
                  </div>
                  <div className="flex space-x-3">
                    <button
                      onClick={() => approveEvent(event.id)}
                      className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2 rounded-full text-sm hover:from-green-600 hover:to-emerald-600 transition-all"
                    >
                      承認
                    </button>
                    <button
                      onClick={() => rejectEvent(event.id)}
                      className="bg-gradient-to-r from-red-500 to-pink-500 text-white px-4 py-2 rounded-full text-sm hover:from-red-600 hover:to-pink-600 transition-all"
                    >
                      却下
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* 承認待ち記事 */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
        <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
          <span className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mr-3">
            <span className="text-white text-sm">📝</span>
          </span>
          承認待ち記事 ({pendingArticles.length})
        </h3>
        {pendingArticles.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">✅</div>
            <p className="text-gray-600">承認待ちの記事はありません。</p>
          </div>
        ) : (
          <div className="space-y-6">
            {pendingArticles.map(article => {
              const comedian = data.comedians.find(c => c.id === article.comedianId);
              return (
                <div key={article.id} className="bg-blue-50 border border-blue-200 rounded-xl p-6">
                  {article.imageURL && (
                    <img
                      src={article.imageURL}
                      alt={article.title}
                      className="w-full h-48 object-cover rounded-lg mb-4"
                    />
                  )}
                  <h4 className="text-xl font-semibold text-gray-800 mb-3">{article.title}</h4>
                  <p className="text-gray-600 leading-relaxed mb-4">{article.content}</p>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2 text-gray-500 text-sm">
                      <span>🎭</span>
                      <span>{comedian?.name}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-gray-500 text-sm">
                      <span>👤</span>
                      <span>{article.submittedBy}</span>
                    </div>
                  </div>
                  <div className="flex space-x-3">
                    <button
                      onClick={() => approveArticle(article.id)}
                      className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2 rounded-full text-sm hover:from-green-600 hover:to-emerald-600 transition-all"
                    >
                      承認
                    </button>
                    <button
                      onClick={() => rejectArticle(article.id)}
                      className="bg-gradient-to-r from-red-500 to-pink-500 text-white px-4 py-2 rounded-full text-sm hover:from-red-600 hover:to-pink-600 transition-all"
                    >
                      却下
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}

export default AdminPage;
