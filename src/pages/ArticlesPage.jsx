import React from 'react';
import { useAppContext } from '../context/AppContext';

function ArticlesPage() {
  const { data } = useAppContext();
  const approvedArticles = data.articles.filter(a => a.isApproved);

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-800 mb-4">記事一覧</h2>
        <p className="text-gray-600">ファンが投稿した記事をチェックしよう</p>
      </div>

      {/* 承認済み記事一覧 */}
      <div>
        <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
          <span className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mr-3">
            <span className="text-white text-sm">📰</span>
          </span>
          承認済み記事
        </h3>
        {approvedArticles.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">📝</div>
            <p className="text-gray-600">まだ承認済みの記事がありません。</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {approvedArticles.map(article => {
              const comedian = data.comedians.find(c => c.id === article.comedianId);
              return (
                <div
                  key={article.id}
                  className="bg-gradient-to-r from-orange-50 to-red-50 border border-orange-100 rounded-xl p-6 shadow-md"
                >
                  {article.imageURL && (
                    <img
                      src={article.imageURL}
                      alt={article.title}
                      className="w-full h-48 object-cover rounded-lg mb-4"
                    />
                  )}
                  <h4 className="text-xl font-semibold text-gray-800 mb-3">{article.title}</h4>
                  <p className="text-gray-600 leading-relaxed mb-4">{article.content}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 text-gray-500 text-sm">
                      <span>🎭</span>
                      <span>{comedian?.name}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-gray-500 text-sm">
                      <span>👤</span>
                      <span>{article.submittedBy}</span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}

export default ArticlesPage;
