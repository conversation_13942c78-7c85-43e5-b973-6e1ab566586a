import React, { useState } from 'react';
import { useAppContext } from '../context/AppContext';

function EventsPage() {
  const { data, updateData } = useAppContext();
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({
    comedianId: '',
    eventName: '',
    date: '',
    venue: '',
    detailsURL: '',
    submittedBy: ''
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    const newEvent = {
      id: Date.now(),
      ...formData,
      comedianId: parseInt(formData.comedianId),
      isApproved: false
    };

    const newData = {
      ...data,
      events: [...data.events, newEvent]
    };
    updateData(newData);
    setFormData({ comedianId: '', eventName: '', date: '', venue: '', detailsURL: '', submittedBy: '' });
    setShowForm(false);
    alert('出演情報を投稿しました！管理者の承認をお待ちください。');
  };

  const approvedEvents = data.events.filter(e => e.isApproved);

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-800 mb-4">出演情報一覧</h2>
        <p className="text-gray-600">最新の出演情報をチェックしよう</p>
      </div>

      {/* 投稿フォームトグルボタン */}
      <div className="flex justify-center">
        <button
          onClick={() => setShowForm(!showForm)}
          className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-2 rounded-full font-semibold shadow-lg hover:from-purple-700 hover:to-blue-700 transition-all"
        >
          {showForm ? '投稿フォームを閉じる' : '新規出演情報を投稿'}
        </button>
      </div>

      {/* 投稿フォーム */}
      {showForm && (
        <form
          onSubmit={handleSubmit}
          className="bg-white rounded-2xl shadow-lg border border-gray-100 max-w-xl mx-auto p-8 space-y-6"
        >
          <div>
            <label className="block mb-1 font-semibold text-gray-700">芸人</label>
            <select
              value={formData.comedianId}
              onChange={e => setFormData({ ...formData, comedianId: e.target.value })}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-purple-400"
              required
            >
              <option value="">芸人を選択</option>
              {data.comedians.map(comedian => (
                <option key={comedian.id} value={comedian.id}>
                  {comedian.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block mb-1 font-semibold text-gray-700">イベント名</label>
            <input
              type="text"
              value={formData.eventName}
              onChange={e => setFormData({ ...formData, eventName: e.target.value })}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-purple-400"
              placeholder="イベント名を入力"
              required
            />
          </div>
          <div>
            <label className="block mb-1 font-semibold text-gray-700">日時</label>
            <input
              type="date"
              value={formData.date}
              onChange={e => setFormData({ ...formData, date: e.target.value })}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-purple-400"
              required
            />
          </div>
          <div>
            <label className="block mb-1 font-semibold text-gray-700">会場</label>
            <input
              type="text"
              value={formData.venue}
              onChange={e => setFormData({ ...formData, venue: e.target.value })}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-purple-400"
              placeholder="会場名を入力"
              required
            />
          </div>
          <div>
            <label className="block mb-1 font-semibold text-gray-700">詳細URL（任意）</label>
            <input
              type="url"
              value={formData.detailsURL}
              onChange={e => setFormData({ ...formData, detailsURL: e.target.value })}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-purple-400"
              placeholder="https://example.com"
            />
          </div>
          <div>
            <label className="block mb-1 font-semibold text-gray-700">投稿者名</label>
            <input
              type="text"
              value={formData.submittedBy}
              onChange={e => setFormData({ ...formData, submittedBy: e.target.value })}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-purple-400"
              placeholder="あなたの名前"
              required
            />
          </div>
          <button
            type="submit"
            className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-2 rounded-full font-semibold shadow-lg hover:from-purple-700 hover:to-blue-700 transition-all"
          >
            投稿
          </button>
        </form>
      )}

      {/* 承認済みイベント一覧 */}
      <div>
        <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
          <span className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mr-3">
            <span className="text-white text-sm">📅</span>
          </span>
          承認済み出演情報
        </h3>
        {approvedEvents.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">📝</div>
            <p className="text-gray-600">まだ承認済みの出演情報がありません。</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {approvedEvents.map(event => {
              const comedian = data.comedians.find(c => c.id === event.comedianId);
              return (
                <div
                  key={event.id}
                  className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-100 rounded-xl p-6 shadow-md"
                >
                  <h4 className="text-xl font-semibold text-gray-800 mb-2">{event.eventName}</h4>
                  <div className="flex flex-col space-y-2 text-gray-600">
                    <span>🎭 芸人: {comedian?.name}</span>
                    <span>📅 日時: {event.date}</span>
                    <span>📍 会場: {event.venue}</span>
                    <span>👤 投稿者: {event.submittedBy}</span>
                    {event.detailsURL && (
                      <a
                        href={event.detailsURL}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-block mt-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2 rounded-full text-sm hover:from-green-600 hover:to-emerald-600 transition-all"
                      >
                        詳細を見る
                      </a>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}

export default EventsPage;
