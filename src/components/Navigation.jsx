import React from 'react';

function Navigation({ currentPage, navigateTo }) {
  const navigationItems = [
    { key: 'home', label: 'トップ', icon: '🏠' },
    { key: 'comedians', label: '芸人一覧', icon: '👥' },
    { key: 'events', label: '出演情報', icon: '📅' },
    { key: 'articles', label: '記事一覧', icon: '📰' },
    { key: 'admin', label: '管理画面', icon: '⚙️' }
  ];

  return (
    <nav className="bg-white border-b border-gray-200 sticky top-0 z-40">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex space-x-8 overflow-x-auto">
          {navigationItems.map(item => (
            <button
              key={item.key}
              onClick={() => navigateTo(item.key)}
              className={`flex items-center space-x-2 py-4 px-2 text-sm font-medium border-b-2 transition-all ${currentPage === item.key
                  ? 'border-purple-500 text-purple-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
            >
              <span>{item.icon}</span>
              <span>{item.label}</span>
            </button>
          ))}
        </div>
      </div>
    </nav>
  );
}

export default Navigation;
